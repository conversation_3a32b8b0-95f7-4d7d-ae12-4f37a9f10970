/***
 * desc：机构管理接口
 * time:2025-09-06
 *
 * */
import { sysApi } from '@/globalSettings'
import http from '@/assets/js/http'

// 分页查询机构列表
export const getOrganizationPage = (params: object) => {
    return http.get(`${sysApi}/organization/page`, params)
}

// 根据ID查询机构详情
export const getOrganizationInfo = (id: number) => {
    return http.get(`${sysApi}/organization/info/${id}`,{})
}

// 保存机构
export const saveOrganization = (params: object) => {
    return http.post(`${sysApi}/organization/save`, params)
}

// 更新机构
export const updateOrganization = (params: object) => {
    return http.post(`${sysApi}/organization/update`, params)
}

// 删除机构
export const deleteOrganization = (id: number) => {
    return http.post(`${sysApi}/organization/del/${id}`, {})
}