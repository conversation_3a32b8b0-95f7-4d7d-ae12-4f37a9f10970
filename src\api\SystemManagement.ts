// 系统管理-模块
import { sysApi } from '@/globalSettings'
import http from '@/assets/js/http'

export default {
  // 数据字典-字典分页列表查询
  getSysDictKeyPage: function (params: object) {
    return http.get(`${sysApi}/sysDictKey/page`, params)
  },
  // 数据字典-新增
  saveSysDictKey: function (params: object) {
    return http.post(`${sysApi}/sysDictKey/save`, params)
  },
  // 数据字典-编辑
  updateSysDictKey: function (params: object) {
    return http.post(`${sysApi}/sysDictKey/update`, params)
  },
  // 数据字典-字典值分页列表查询
  getSysDictValuePage: function (params: object) {
    return http.get(`${sysApi}/sysDictValue/page`, params)
  },
  // 数据字典-字典值新增
  saveSysDictValue: function (params: object) {
    return http.post(`${sysApi}/sysDictValue/save`, params)
  },
  // 数据字典-字典值编辑
  updateSysDictValue: function (params: object) {
    return http.post(`${sysApi}/sysDictValue/update`, params)
  },
  // 数据字典-字典值删除
  delSysDictValue: function (params: object) {
    return http.post(`${sysApi}/sysDictValue/del/${params.id}`, params)
  }
}
