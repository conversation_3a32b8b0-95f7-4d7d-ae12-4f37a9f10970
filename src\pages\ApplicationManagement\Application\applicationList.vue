<template>
  <div class="application-list">
    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <el-row :gutter="20">
        <el-col :span="4">
          <el-select v-model="searchForm.appName" placeholder="应用名称" clearable filterable>
            <el-option
              v-for="option in appOptions"
              :key="option.value"
              :label="option.label"
              :value="option.label"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.productName" placeholder="产品名称" clearable filterable>
            <el-option
              v-for="option in productOptions"
              :key="option.value"
              :label="option.label"
              :value="option.label"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.status" placeholder="状态" clearable>
            <el-option
              v-for="option in statusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.type" placeholder="类型" clearable>
            <el-option
              v-for="option in typeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.organizationName" placeholder="所属机构" clearable filterable>
            <el-option
              v-for="option in organizationOptions"
              :key="option.value"
              :label="option.label"
              :value="option.label"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
          :data="tableData"
          v-loading="loading"
          stripe
          style="width: 100%"
          class="center-table"
      >
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="appName" label="应用名称" min-width="100" />
        <el-table-column prop="productNames" label="所属产品" min-width="100" />
        <el-table-column prop="status" label="状态" width="100" sortable>
          <template #default="{ row }">
              {{ getStatusText(row.status) }}
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" width="120">
          <template #default="{ row }">
              {{ getTypeText(row.type) }}
          </template>
        </el-table-column>
        <el-table-column prop="organizationName" label="所属组/公司" min-width="120" />
        <el-table-column prop="moduleCount" label="模块数量" width="120" sortable />
        <el-table-column prop="totalPublishCount" label="总发布次数" width="160" sortable />
        <el-table-column prop="totalPublishDuration" label="总发布时长" width="160" sortable>
          <template #default="{ row }">
            {{ formatDuration(row.totalPublishDuration) }}
          </template>
        </el-table-column>
        <el-table-column prop="avgPublishDuration" label="平均发布时长" width="160" sortable>
          <template #default="{ row }">
            {{ formatDuration(row.avgPublishDuration) }}
          </template>
        </el-table-column>
        <el-table-column prop="avgStartupDuration" label="平均启动时长" width="160" sortable>
          <template #default="{ row }">
            {{ formatDuration(row.avgStartupDuration) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="info" size="small" @click="handleView(row)" :loading="detailLoading">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 应用详情弹窗 -->
    <el-dialog
      v-model="detailVisible"
      title="应用详情"
      width="90%"
      :close-on-click-modal="true"
      class="detail-dialog"
    >
      <ApplicationDetail
        :app-id="currentAppId"
        :visible="detailVisible"
        :preloaded="true"
        :preloaded-data="preloadedDetailData"
        @update:visible="detailVisible = $event"
      />
    </el-dialog>

    <!-- 编辑应用信息弹窗 -->
    <EditApplicationDialog
      v-model="editVisible"
      :edit-id="currentEditId"
      @success="handleEditSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import ApplicationApi, { type PamsAppSummaryInfoQueryParams, type PamsAppSummaryInfoPageVO, type DropdownOptionVO, type SysDictValueDO } from '@/api/Application'
import ApplicationDetail from './components/applicationDetail.vue'
import {dictList} from '@/api/CommApi'
import EditApplicationDialog from './components/EditApplicationDialog.vue'

// 搜索表单
const searchForm = reactive<PamsAppSummaryInfoQueryParams>({
  appName: '',
  productName: '',
  status: '',
  type: '',
  organizationName: ''
})

// 表格数据
const tableData = ref<PamsAppSummaryInfoPageVO[]>([])
const loading = ref(false)

// 下拉框选项数据
const appOptions = ref<DropdownOptionVO[]>([])
const productOptions = ref<DropdownOptionVO[]>([])
const organizationOptions = ref<DropdownOptionVO[]>([])
const statusOptions = ref<SysDictValueDO[]>([])
const typeOptions = ref<SysDictValueDO[]>([])

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 5
})

// 获取状态文本
const getStatusText = (status: string): string => {
  const statusOption = statusOptions.value.find(option => option.value === status)
  return statusOption ? statusOption.label : status
}

// 获取类型文本
const getTypeText = (type: string): string => {
  // 从字典选项中查找对应的标签
  const typeOption = typeOptions.value.find(option => option.value === type)
  return typeOption ? typeOption.label : type
}

// 格式化时长（毫秒转换为可读格式）
const formatDuration = (milliseconds: number | null | undefined): string => {
  if (!milliseconds || milliseconds === 0) return '-'

  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)

  if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${seconds % 60}秒`
  } else {
    return `${seconds}秒`
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    appName: '',
    productName: '',
    status: '',
    type: '',
    organizationName: ''
  })
  handleSearch()
}


// 编辑
const handleEdit = (row: any) => {
  currentEditId.value = row.id
  editVisible.value = true
}

// 编辑成功回调
const handleEditSuccess = () => {
  editVisible.value = false
  currentEditId.value = undefined
  // 重新加载数据
  loadData()
}

// 详情弹窗相关
const detailVisible = ref(false)
const currentAppId = ref<number | null>(null)
const detailLoading = ref(false)
const preloadedDetailData = ref<any>(null)

// 编辑弹窗相关
const editVisible = ref(false)
const currentEditId = ref<number | undefined>(undefined)

// 查看详情
const handleView = async (row: any) => {
  try {
    detailLoading.value = true
    currentAppId.value = row.id

    // 确保字典数据已加载
    if (statusOptions.value.length === 0 || typeOptions.value.length === 0) {
      await loadDropdownOptions()
    }

    // 预加载详情数据
    const detailResponse = await ApplicationApi.getAppDetail(row.id)

    if (!detailResponse.success || !detailResponse.data) {
      throw new Error('获取应用详情失败')
    }

    preloadedDetailData.value = {
      detail: detailResponse.data,
      statusOptions: statusOptions.value,
      typeOptions: typeOptions.value
    }

    // 数据加载成功后显示弹窗
    detailVisible.value = true
  } catch (error) {
    console.error('加载应用详情失败:', error)
    ElMessage.error('加载应用详情失败，请重试')
  } finally {
    detailLoading.value = false
  }
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  loadData()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadData()
}

// 加载下拉框选项数据
const loadDropdownOptions = async () => {
  try {
    // 并行加载所有下拉框选项
    const [appResponse, productResponse, organizationResponse, statusResponse, typeResponse] = await Promise.all([
      ApplicationApi.getAppDropdownOptions(),
      ApplicationApi.getProductDropdownOptions(),
      ApplicationApi.getOrganizationDropdownOptions(),
      dictList('app_status'),
      dictList('app_type')
    ])

    if (appResponse.success && appResponse.data) {
      appOptions.value = appResponse.data
    }

    if (productResponse.success && productResponse.data) {
      productOptions.value = productResponse.data
    }

    if (organizationResponse.success && organizationResponse.data) {
      organizationOptions.value = organizationResponse.data
    }

    if (statusResponse.success && statusResponse.data) {
      statusOptions.value = statusResponse.data
    }

    if (typeResponse.success && typeResponse.data) {
      typeOptions.value = typeResponse.data
    }
  } catch (error) {
    console.error('加载下拉框选项失败:', error)
  }
}

// 加载数据
const loadData = async () => {
  loading.value = true

  try {
    // 构建查询参数
    const queryParams: PamsAppSummaryInfoQueryParams = {
      pageNum: pagination.page,
      pageSize: pagination.pageSize,
      ...searchForm
    }

    // 调用API
    const response = await ApplicationApi.getPageInfo(queryParams)

    if (response.success && response.data) {
      // 更新表格数据
      tableData.value = response.data.records
      // 更新分页信息
      pagination.total = response.data.total
      pagination.page = response.data.current
      pagination.pageSize = response.data.size
    } else {
      ElMessage.error(response.message || '查询失败')
      tableData.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('查询应用信息失败:', error)
    ElMessage.error('查询失败，请稍后重试')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  // 优先加载字典数据，确保在用户查看详情时已经可用
  await loadDropdownOptions()
  // 然后加载表格数据
  loadData()
})
</script>

<style scoped>
/* 详情弹窗样式 */
:deep(.detail-dialog .el-dialog__body) {
  padding: 20px;
}

/* 分页样式 */
.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 表格居中样式 */
:deep(.center-table .el-table__header th) {
  text-align: center !important;
}

:deep(.center-table .el-table__body td) {
  text-align: center !important;
}
</style>
