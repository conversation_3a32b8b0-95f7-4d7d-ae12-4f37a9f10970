<template>
  <el-dialog
      v-model="visible"
      title="编辑应用信息"
      width="1200px"
      @close="handleClose"
      :close-on-click-modal="false"
      class="edit-app-dialog"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="140px" class="edit-form">
      <el-row :gutter="20">
        <!-- 左列 -->
        <el-col :span="12">
          <!-- 基础信息 -->
          <div class="form-section">
            <h3 class="section-title">基础信息</h3>
            <el-form-item label="应用名称" prop="appName">
              <el-input v-model="form.appName" disabled placeholder="应用名称"/>
            </el-form-item>
            <el-form-item label="所属产品" prop="productNames">
              <el-select v-model="form.productNames" disabled placeholder="所属产品" style="width: 100%">
                <el-option
                    v-for="option in productOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.label"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
                <el-option
                    v-for="option in statusOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择类型" style="width: 100%">
                <el-option
                    v-for="option in typeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="所属组/公司" prop="organizationId">
              <el-select v-model="form.organizationId" placeholder="请选择组/公司" style="width: 100%">
                <el-option
                    v-for="option in organizationOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>
        </el-col>

        <!-- 右列 -->
        <el-col :span="12">
          <!-- 团队信息 -->
          <div class="form-section">
            <h3 class="section-title">团队信息</h3>
            <el-form-item label="负责开发组" prop="devTeam">
              <el-select v-model="form.devTeam" disabled placeholder="负责开发组" style="width: 100%">
                <el-option
                    v-for="option in devTeamOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.label"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="负责测试组" prop="testTeam">
              <el-select v-model="form.testTeam" placeholder="请选择测试组" style="width: 100%">
                <el-option
                    v-for="option in testTeamOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="负责项目经理" prop="projectManager">
              <el-select v-model="form.projectManager" placeholder="请选择项目经理" style="width: 100%">
                <el-option
                    v-for="option in projectManagerOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>
        </el-col>
      </el-row>


      <!-- 分类信息 -->
      <div class="form-section">
        <h3 class="section-title">分类信息</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="应用大类" prop="appMajorCategory">
              <el-select
                  v-model="form.appMajorCategory"
                  placeholder="请选择应用大类"
                  style="width: 100%"
                  @change="handleMajorCategoryChange"
              >
                <el-option
                    v-for="option in majorCategoryOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="应用小类" prop="appMinorCategory">
              <el-select
                  v-model="form.appMinorCategory"
                  placeholder="请选择应用小类"
                  style="width: 100%"
              >
                <el-option
                    v-for="option in minorCategoryOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="form-section">
        <h3 class="section-title">系统标识</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="SSO 系统标识" prop="ssoSystemFlag">
              <el-input v-model="form.ssoSystemFlag" placeholder="请输入 SSO 系统标识"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="源码工程标识" prop="sourceProjectFlag">
              <el-input v-model="form.sourceProjectFlag" placeholder="请输入源码工程标识"/>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <div class="form-section">
        <h3 class="section-title">扩展信息</h3>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="生产应用标识" prop="prodAppIdentifier">
              <el-input v-model="form.prodAppIdentifier" disabled placeholder="生产应用标识"/>
            </el-form-item>

            <el-form-item label="Git 地址及分支" prop="gitAddress">
              <el-input v-model="form.gitAddress" disabled placeholder="Git 地址及分支"/>
            </el-form-item>

            <el-form-item label="数据库标识" prop="databaseFlag">
              <el-input v-model="form.databaseFlag" placeholder="请输入数据库标识"/>
            </el-form-item>

            <el-form-item label="OSS 私有文件桶标识" prop="ossPrivateBucketFlag">
              <el-input v-model="form.ossPrivateBucketFlag" placeholder="请输入 OSS 私有文件桶标识"/>
            </el-form-item>

            <el-form-item label="开发语言" prop="developLanguage">
              <el-input v-model="form.developLanguage" disabled placeholder="开发语言"/>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import {ref, reactive, watch, computed} from 'vue'
import {ElMessage, type FormInstance, type FormRules} from 'element-plus'
import ApplicationApi, {
  type PamsAppSummaryInfoDetailVO,
  type DropdownOptionVO,
  type SysDictValueDO
} from '@/api/Application'
import {dictList} from '@/api/CommApi'

interface Props {
  modelValue: boolean
  editId?: number
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void

  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  editId: undefined
})

const emit = defineEmits<Emits>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => !!props.editId)

const formRef = ref<FormInstance>()
const submitLoading = ref(false)
const loading = ref(false)

// 表单数据
const form = reactive<Partial<PamsAppSummaryInfoDetailVO>>({
  appName: '',
  productNames: '',
  status: '',
  type: '',
  organizationId: 0,
  devTeam: '',
  testTeam: '',
  projectManager: '',
  appMajorCategory: '',
  appMinorCategory: '',
  ssoSystemFlag: '',
  sourceProjectFlag: '',
  prodAppIdentifier: '',
  gitAddress: '',
  databaseFlag: '',
  ossPrivateBucketFlag: '',
  developLanguage: ''
})

// 下拉框选项数据
const productOptions = ref<DropdownOptionVO[]>([])
const organizationOptions = ref<DropdownOptionVO[]>([])
const statusOptions = ref<SysDictValueDO[]>([])
const typeOptions = ref<SysDictValueDO[]>([])
const devTeamOptions = ref<DropdownOptionVO[]>([])
const testTeamOptions = ref<SysDictValueDO[]>([])
const projectManagerOptions = ref<SysDictValueDO[]>([])
const majorCategoryOptions = ref<SysDictValueDO[]>([])
const minorCategoryOptions = ref<SysDictValueDO[]>([])

// 表单验证规则
const rules: FormRules = {
  status: [
    {required: true, message: '请选择状态', trigger: 'change'}
  ],
  type: [
    {required: true, message: '请选择类型', trigger: 'change'}
  ],
  organizationId: [
    {required: true, message: '请选择所属组/公司', trigger: 'change'}
  ],
  testTeam: [
    {required: true, message: '请选择负责测试组', trigger: 'change'}
  ],
  projectManager: [
    {required: true, message: '请选择负责项目经理', trigger: 'change'}
  ],
  appMajorCategory: [
    {required: true, message: '请选择应用大类', trigger: 'change'}
  ],
  appMinorCategory: [
    {required: true, message: '请选择应用小类', trigger: 'change'}
  ]
}

// 加载下拉框选项数据
const loadDropdownOptions = async () => {
  try {
    const [
      productResponse,
      organizationResponse,
      statusResponse,
      typeResponse,
      majorCategoryResponse,
      testTeamResponse,
      projectManagerResponse
    ] = await Promise.all([
      ApplicationApi.getProductDropdownOptions(),
      ApplicationApi.getOrganizationDropdownOptions(),
      dictList('app_status'),
      dictList('app_type'),
      dictList('app_major_category'),
      dictList('app_test_dept'),
      dictList('app_project_manage')
    ])

    if (productResponse.success && productResponse.data) {
      productOptions.value = productResponse.data
    }

    if (organizationResponse.success && organizationResponse.data) {
      organizationOptions.value = organizationResponse.data
    }

    if (statusResponse.success && statusResponse.data) {
      statusOptions.value = statusResponse.data
    }

    if (typeResponse.success && typeResponse.data) {
      typeOptions.value = typeResponse.data
    }

    if (majorCategoryResponse.success && majorCategoryResponse.data) {
      majorCategoryOptions.value = majorCategoryResponse.data
    }

    if (testTeamResponse.success && testTeamResponse.data) {
      testTeamOptions.value = testTeamResponse.data
    }

    if (projectManagerResponse.success && projectManagerResponse.data) {
      projectManagerOptions.value = projectManagerResponse.data
    }

  } catch (error) {
    console.error('加载下拉框选项失败:', error)
    ElMessage.error('加载选项数据失败')
  }
}

// 应用大类变化处理
const handleMajorCategoryChange = async (value: string) => {
  form.appMinorCategory = '' // 清空小类
  if (!value) {
    minorCategoryOptions.value = []
    return
  }

  // 如果小类选项还没有加载，则加载所有小类选项
  if (minorCategoryOptions.value.length === 0) {
    await loadMinorCategoryOptions()
  }
}

// 加载应用小类选项
const loadMinorCategoryOptions = async () => {
  try {
    const response = await dictList('app_minor_category')
    if (response.success && response.data) {
      minorCategoryOptions.value = response.data
    } else {
      minorCategoryOptions.value = []
    }
  } catch (error) {
    console.error('加载应用小类失败:', error)
    minorCategoryOptions.value = []
  }
}

// 加载编辑数据
const loadEditData = async () => {
  if (!props.editId) return

  loading.value = true
  try {
    const response = await ApplicationApi.getAppDetail(props.editId)
    if (response.success && response.data) {
      Object.assign(form, response.data)
    } else {
      ElMessage.error('获取应用详情失败')
    }
  } catch (error) {
    console.error('加载编辑数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    appName: '',
    productNames: '',
    status: '',
    type: '',
    organizationId: 0,
    devTeam: '',
    testTeam: '',
    projectManager: '',
    appMajorCategory: '',
    appMinorCategory: '',
    ssoSystemFlag: '',
    sourceProjectFlag: '',
    prodAppIdentifier: '',
    gitAddress: '',
    databaseFlag: '',
    ossPrivateBucketFlag: '',
    developLanguage: ''
  })
  formRef.value?.resetFields()
  minorCategoryOptions.value = []
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitLoading.value = true

    // 构建更新参数
    const updateParams = {
      id: props.editId!,
      status: form.status,
      type: form.type,
      organizationId: form.organizationId,
      testTeam: form.testTeam,
      projectManager: form.projectManager,
      appMajorCategory: form.appMajorCategory,
      appMinorCategory: form.appMinorCategory,
      ssoSystemFlag: form.ssoSystemFlag,
      sourceProjectFlag: form.sourceProjectFlag,
      databaseFlag: form.databaseFlag,
      ossPrivateBucketFlag: form.ossPrivateBucketFlag
    }

    const response = await ApplicationApi.updateApp(updateParams)

    if (response.success) {
      ElMessage.success('编辑成功')
      emit('success')
      handleClose()
    } else {
      ElMessage.error(response.message || '编辑失败')
    }

  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请稍后重试')
  } finally {
    submitLoading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  resetForm()
}

// 监听编辑ID变化
watch(() => props.editId, (newId) => {
  if (newId) {
    loadEditData()
  } else {
    resetForm()
  }
})

// 监听对话框显示状态
watch(visible, (newVisible) => {
  if (newVisible) {
    loadDropdownOptions()
    loadMinorCategoryOptions() // 加载小类选项
    if (props.editId) {
      loadEditData()
    } else {
      resetForm()
    }
  }
})
</script>

<style scoped>
.edit-app-dialog {
  margin-top: 5vh;
}

:deep(.el-dialog__body) {
  padding: 20px 30px;
}

.edit-form {
  max-height: 70vh;
  overflow-y: auto;
  padding: 20px;
}

.form-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 20px 0;
  padding-left: 12px;
  position: relative;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: #409EFF;
  border-radius: 2px;
}

/* 必填项标识 */
:deep(.el-form-item.is-required > .el-form-item__label::before) {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

/* 禁用状态样式 */
:deep(.el-input.is-disabled .el-input__inner) {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
}

:deep(.el-select.is-disabled .el-input__inner) {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .edit-app-dialog {
    width: 95% !important;
    margin: 5px auto;
  }

  :deep(.el-col) {
    margin-bottom: 20px;
  }
}
</style>
