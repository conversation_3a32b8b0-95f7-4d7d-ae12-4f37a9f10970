<template>
  <div class="application-detail-component">

    <!-- 基本信息 -->
    <el-card class="detail-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="header-title">基本信息</span>
        </div>
      </template>
      

      <!-- 基本信息表单 -->
      <el-descriptions :column="descriptionsColumn" border>
        <el-descriptions-item v-for="field in basicInfoFields" :key="field.key" :label="field.label">
          <!-- Git地址 -->
          <el-link v-if="field.type === 'link' && field.value !== '-'" 
                   :href="field.value" 
                   target="_blank" 
                   type="primary" 
                   class="git-link">
            {{ field.value }}
          </el-link>
          <!-- 标签类型字段 -->
          <el-tag v-else-if="field.type === 'tag' && field.value !== '-'" 
                  :type="field.tagType || 'info'" 
                  effect="light"
                  size="default">
            {{ field.value }}
          </el-tag>
          <!-- 普通文本 -->
          <span v-else class="field-text">{{ field.value }}</span>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 更多信息 -->
    <el-card class="detail-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="header-title">更多信息</span>
        </div>
      </template>

      <el-tabs v-model="activeTab" class="detail-tabs">
        <!-- 依赖选项卡 -->
        <el-tab-pane label="依赖" name="dependencies">
          <el-table :data="dependenciesData" border style="width: 100%" class="center-table">
            <el-table-column prop="groupId" label="groupId" align="center" header-align="center" />
            <el-table-column prop="artifactId" label="artifactId" align="center" header-align="center" />
            <el-table-column prop="version" label="version" align="center" header-align="center" />
          </el-table>
        </el-tab-pane>

        <!-- 应用日志选项卡 -->
        <el-tab-pane label="应用日志" name="appLogs">
          <el-table :data="appLogsData" border style="width: 100%" class="center-table">
            <el-table-column prop="serverIp" label="服务器IP" align="center" header-align="center" />
            <el-table-column prop="logPath" label="日志地址" align="center" header-align="center" />
            <el-table-column prop="logFileCount" label="日志文件数量" align="center" header-align="center" />
            <el-table-column prop="largeFileCount" label="超过3G的文件数量" align="center" header-align="center" />
            <el-table-column prop="maxFileSize" label="最大文件存储量" align="center" header-align="center" />
            <el-table-column prop="createTime" label="创建时间" align="center" header-align="center" />
            <el-table-column prop="updateTime" label="更新时间" align="center" header-align="center" />
          </el-table>
        </el-tab-pane>

        <!-- 日志清理记录选项卡 -->
        <el-tab-pane label="日志清理记录" name="logCleanup">
          <div class="table-header">
            <el-button type="primary" @click="handleAddCleanupRecord">添加</el-button>
          </div>
          <el-table
            :data="logCleanupData"
            border
            style="width: 100%"
            class="center-table"
            v-loading="logCleanupLoading">
            <el-table-column prop="serverIp" label="服务器IP" align="center" header-align="center" />
            <el-table-column prop="cleanFilePath" label="清理文件路径" align="center" header-align="center" />
            <el-table-column prop="cleanFileName" label="清理文件名称" align="center" header-align="center" />
            <el-table-column prop="cleanTime" label="清理时间" align="center" header-align="center" />
            <el-table-column prop="operationType" label="操作类型" align="center" header-align="center">
              <template #default="scope">
                <el-tag :type="scope.row.operationType === 'MANUAL' ? 'primary' : 'success'">
                  {{ getOperationTypeText(scope.row.operationType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="cleanReason" label="清理原因" align="center" header-align="center">
              <template #default="scope">
                <span>{{ getCleanReasonText(scope.row.cleanReason) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="operationResult" label="操作结果" align="center" header-align="center">
              <template #default="scope">
                <el-tag :type="scope.row.operationResult === 'SUCCESS' ? 'success' : 'danger'">
                  {{ getOperationResultText(scope.row.operationResult) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="failureReason" label="失败原因" align="center" header-align="center" />
            <el-table-column prop="sizeBeforeClean" label="清理前大小" align="center" header-align="center">
              <template #default="scope">
                <span>{{ formatFileSize(scope.row.sizeBeforeClean) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="sizeAfterClean" label="清理后大小" align="center" header-align="center">
              <template #default="scope">
                <span>{{ formatFileSize(scope.row.sizeAfterClean) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80" align="center" header-align="center">
              <template #default="scope">
                <el-button type="primary" size="small" @click="handleEditCleanupRecord(scope.row)">
                  编辑
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页组件 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="logCleanupPagination.current"
              v-model:page-size="logCleanupPagination.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="logCleanupPagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleLogCleanupSizeChange"
              @current-change="handleLogCleanupPageChange"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 日志清理记录表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        v-loading="formLoading">
        <el-form-item label="服务器IP" prop="serverIp">
          <el-input v-model="formData.serverIp" placeholder="请输入服务器IP" />
        </el-form-item>

        <el-form-item label="清理文件路径" prop="cleanFilePath">
          <el-input v-model="formData.cleanFilePath" placeholder="请输入清理文件路径" />
        </el-form-item>

        <el-form-item label="清理文件名称" prop="cleanFileName">
          <el-input v-model="formData.cleanFileName" placeholder="请输入清理文件名称" />
        </el-form-item>

        <el-form-item label="清理时间" prop="cleanTime">
          <el-date-picker
            v-model="formData.cleanTime"
            type="datetime"
            placeholder="请选择日期"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="操作类型" prop="operationType">
          <el-select v-model="formData.operationType" placeholder="请选择操作类型" style="width: 100%">
            <el-option
              v-for="item in operationTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="清理原因" prop="cleanReason">
          <el-select v-model="formData.cleanReason" placeholder="请选择清理原因" style="width: 100%">
            <el-option
              v-for="item in cleanReasonOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="操作结果" prop="operationResult">
          <el-select v-model="formData.operationResult" placeholder="请选择操作结果" style="width: 100%">
            <el-option
              v-for="item in operationResultOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="失败原因" prop="failureReason">
          <el-input
            v-model="formData.failureReason"
            type="textarea"
            :rows="3"
            placeholder="请输入失败原因"
          />
        </el-form-item>

        <el-form-item label="清理前大小" prop="sizeBeforeClean">
          <el-input
            v-model.number="formData.sizeBeforeClean"
            placeholder="请输入清理前大小(字节)"
            type="number"
          />
        </el-form-item>

        <el-form-item label="清理后大小" prop="sizeAfterClean">
          <el-input
            v-model.number="formData.sizeAfterClean"
            placeholder="请输入清理后大小(字节)"
            type="number"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleSave" :loading="formLoading">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, withDefaults, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import ApplicationApi, {
  type PamsAppSummaryInfoDetailVO,
  type SysDictValueDO
} from '@/api/Application'
import { dictList } from '@/api/CommApi'

// 组件属性
interface Props {
  appId: number | null
  visible: boolean
  preloaded?: boolean // 标识数据是否已经预加载
  preloadedData?: any // 预加载的数据
}

const props = withDefaults(defineProps<Props>(), {
  preloaded: false,
  preloadedData: null
})



// 加载状态
const loading = ref(false)

// 当前选中的选项卡
const activeTab = ref('dependencies')

// 屏幕宽度和描述列表列数
const screenWidth = ref(window.innerWidth)
const descriptionsColumn = computed(() => {
  if (screenWidth.value <= 768) return 1
  if (screenWidth.value <= 1200) return 2
  return 3
})



// 监听窗口大小变化
const handleResize = () => {
  screenWidth.value = window.innerWidth
}

onMounted(async () => {
  window.addEventListener('resize', handleResize)
  // 预先加载字典数据
  await loadDictOptions()
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 依赖数据接口
interface DependencyData {
  groupId: string
  artifactId: string
  version: string
}

// 应用日志数据接口
interface AppLogData {
  serverIp: string
  logPath: string
  logFileCount: number
  largeFileCount: number
  maxFileSize: string
  createTime: string
  updateTime: string
}

// 日志清理记录数据接口
interface LogCleanupData {
  id?: number
  serverIp: string
  cleanFilePath: string
  cleanFileName: string
  cleanTime: string
  operationType: string
  cleanReason: string
  operationResult: string
  failureReason?: string
  sizeBeforeClean?: number
  sizeAfterClean?: number
}

// 各种数据
const dependenciesData = ref<DependencyData[]>([])
const appLogsData = ref<AppLogData[]>([])
const logCleanupData = ref<LogCleanupData[]>([])



// 日志清理记录分页相关
const logCleanupLoading = ref(false)
const logCleanupPagination = ref({
  current: 1,
  pageSize: 10,
  total: 0
})

// 表单相关状态
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)
const formLoading = ref(false)

// 表单数据
const formData = ref<LogCleanupData>({
  serverIp: '',
  cleanFilePath: '',
  cleanFileName: '',
  cleanTime: '',
  operationType: '',
  cleanReason: '',
  operationResult: '',
  failureReason: '',
  sizeBeforeClean: undefined,
  sizeAfterClean: undefined
})

// 表单引用
const formRef = ref()

// 表单验证规则
const formRules = {
  serverIp: [
    { required: true, message: '请输入服务器IP', trigger: 'blur' }
  ],
  cleanFilePath: [
    { required: true, message: '请输入清理文件路径', trigger: 'blur' }
  ],
  cleanFileName: [
    { required: true, message: '请输入清理文件名称', trigger: 'blur' }
  ],
  cleanTime: [
    { required: true, message: '请选择清理时间', trigger: 'change' }
  ],
  operationType: [
    { required: true, message: '请选择操作类型', trigger: 'change' }
  ],
  cleanReason: [
    { required: true, message: '请选择清理原因', trigger: 'change' }
  ],
  operationResult: [
    { required: true, message: '请选择操作结果', trigger: 'change' }
  ]
}



// 详情数据
const detailData = ref<PamsAppSummaryInfoDetailVO>({
  id: 0,
  appId: 0,
  appName: '',
  status: '',
  type: '',
  organizationId: 0,
  moduleCount: 0,
  totalPublishCount: 0,
  totalPublishDuration: 0,
  avgPublishDuration: 0,
  avgStartupDuration: 0,
  devTeam: '',
  prodAppIdentifier: '',
  gitAddress: '',
  developLanguage: '',
  testTeam: '',
  projectManager: '',
  appMajorCategory: '',
  appMinorCategory: '',
  ssoSystemFlag: '',
  sourceProjectFlag: '',
  databaseFlag: '',
  ossPrivateBucketFlag: '',
  createTime: '',
  updateTime: '',
  creator: '',
  updater: '',
  deleted: false,
  organizationName: '',
  productNames: ''
})

// 字典选项
const statusOptions = ref<SysDictValueDO[]>([])
const typeOptions = ref<SysDictValueDO[]>([])
const operationTypeOptions = ref<SysDictValueDO[]>([])
const cleanReasonOptions = ref<SysDictValueDO[]>([])
const operationResultOptions = ref<SysDictValueDO[]>([])

// 格式化时长（毫秒转换为可读格式）
const formatDuration = (milliseconds: number | null | undefined): string => {
  if (!milliseconds || milliseconds === 0) return '-'

  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)

  if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${seconds % 60}秒`
  } else {
    return `${seconds}秒`
  }
}

// 获取状态类型
const getStatusType = (status: string): 'success' | 'info' | 'warning' | 'primary' | 'danger' => {
  const typeMap: Record<string, 'success' | 'info' | 'warning' | 'primary' | 'danger'> = {
    start: 'success',
    stop: 'info',
    maintain: 'warning',
    enabled: 'success',
    disabled: 'info',
    maintenance: 'warning'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string): string => {
  const statusOption = statusOptions.value.find(option => option.value === status)
  return statusOption ? statusOption.label : status
}

// 获取类型文本
const getTypeText = (type: string): string => {
  const typeOption = typeOptions.value.find(option => option.value === type)
  return typeOption ? typeOption.label : type
}

// 基本信息字段接口
interface BasicInfoField {
  key: string
  label: string
  value: string
  type?: 'text' | 'tag' | 'link'
  tagType?: 'success' | 'info' | 'warning' | 'primary' | 'danger'
}

// 基本信息字段配置
const basicInfoFields = computed((): BasicInfoField[] => {
  return [
    { 
      key: 'appName', 
      label: '应用名称', 
      value: detailData.value.appName || '-' 
    },
    { 
      key: 'productNames', 
      label: '所属产品', 
      value: detailData.value.productNames || '-' 
    },
    { 
      key: 'organizationName', 
      label: '所属组/公司', 
      value: detailData.value.organizationName || '-' 
    },
    { 
      key: 'devTeam', 
      label: '负责开发组', 
      value: detailData.value.devTeam || '-' 
    },
    { 
      key: 'projectManager', 
      label: '负责项目经理', 
      value: detailData.value.projectManager || '-' 
    },
    { 
      key: 'testTeam', 
      label: '负责测试组', 
      value: detailData.value.testTeam || '-' 
    },
    { 
      key: 'developLanguage', 
      label: '开发语言', 
      value: detailData.value.developLanguage || '-',
      type: 'tag',
      tagType: 'info'
    },
    { 
      key: 'status', 
      label: '状态', 
      value: getStatusText(detailData.value.status),
      type: 'tag',
      tagType: getStatusType(detailData.value.status)
    },
    { 
      key: 'gitAddress', 
      label: 'Git地址', 
      value: detailData.value.gitAddress || '-',
      type: 'link'
    },
    { 
      key: 'appMajorCategory', 
      label: '应用大类', 
      value: detailData.value.appMajorCategory || '-' 
    },
    { 
      key: 'appMinorCategory', 
      label: '应用小类', 
      value: detailData.value.appMinorCategory || '-' 
    },
    { 
      key: 'avgPublishDuration', 
      label: '平均发布时长', 
      value: formatDuration(detailData.value.avgPublishDuration) 
    },
    { 
      key: 'totalPublishDuration', 
      label: '总发布时长', 
      value: formatDuration(detailData.value.totalPublishDuration) 
    },
    { 
      key: 'totalPublishCount', 
      label: '总发布次数', 
      value: String(detailData.value.totalPublishCount || 0) 
    },
    { 
      key: 'type', 
      label: '类型', 
      value: getTypeText(detailData.value.type),
      type: 'tag'
    },
    { 
      key: 'avgStartupDuration', 
      label: '平均启动时长', 
      value: formatDuration(detailData.value.avgStartupDuration) 
    },
    { 
      key: 'prodAppIdentifier', 
      label: '生产应用标识', 
      value: detailData.value.prodAppIdentifier || '-' 
    },
    { 
      key: 'sourceProjectFlag', 
      label: '源码工程标识', 
      value: detailData.value.sourceProjectFlag || '-' 
    },
    { 
      key: 'ossPrivateBucketFlag', 
      label: 'OSS私有文件桶标识', 
      value: detailData.value.ossPrivateBucketFlag || '-' 
    },
    { 
      key: 'databaseFlag', 
      label: '数据库标识', 
      value: detailData.value.databaseFlag || '-' 
    },
    { 
      key: 'ssoSystemFlag', 
      label: 'SSO系统标识', 
      value: detailData.value.ssoSystemFlag || '-' 
    }
  ]
})




// 加载字典数据
const loadDictOptions = async () => {
  try {
    const [statusResponse, typeResponse, operationTypeResponse, cleanReasonResponse, operationResultResponse] = await Promise.all([
      dictList('app_status'),
      dictList('app_type'),
      dictList('log_clean_record_status'),
      dictList('log_clean_record_clean_reason'),
      dictList('log_clean_record_result')
    ])

    if (statusResponse.success && statusResponse.data) {
      statusOptions.value = statusResponse.data
    }

    if (typeResponse.success && typeResponse.data) {
      typeOptions.value = typeResponse.data
    }

    if (operationTypeResponse.success && operationTypeResponse.data) {
      operationTypeOptions.value = operationTypeResponse.data
    }

    if (cleanReasonResponse.success && cleanReasonResponse.data) {
      cleanReasonOptions.value = cleanReasonResponse.data
    }

    if (operationResultResponse.success && operationResultResponse.data) {
      operationResultOptions.value = operationResultResponse.data
    }
  } catch (error) {
    console.error('加载字典数据失败:', error)
  }
}

// 加载应用详情
const loadAppDetail = async () => {
  if (!props.appId) {
    return
  }

  loading.value = true
  try {
    const response = await ApplicationApi.getAppDetail(props.appId)
    if (response.success && response.data) {
      detailData.value = response.data
    } else {
      ElMessage.error('获取应用详情失败')
    }
  } catch (error) {
    console.error('获取应用详情失败:', error)
    ElMessage.error('获取应用详情失败')
  } finally {
    loading.value = false
  }
}

// 格式化文件大小
const formatFileSize = (bytes: number | undefined): string => {
  if (!bytes || bytes === 0) return '-'

  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

// 获取操作类型文本
const getOperationTypeText = (type: string): string => {
  const typeOption = operationTypeOptions.value.find(option => option.value === type)
  return typeOption ? typeOption.label : type
}

// 获取清理原因文本
const getCleanReasonText = (reason: string): string => {
  const reasonOption = cleanReasonOptions.value.find(option => option.value === reason)
  return reasonOption ? reasonOption.label : reason
}

// 获取操作结果文本
const getOperationResultText = (result: string): string => {
  const resultOption = operationResultOptions.value.find(option => option.value === result)
  return resultOption ? resultOption.label : result
}

// 重置表单数据
const resetFormData = () => {
  formData.value = {
    serverIp: '',
    cleanFilePath: '',
    cleanFileName: '',
    cleanTime: '',
    operationType: '',
    cleanReason: '',
    operationResult: '',
    failureReason: '',
    sizeBeforeClean: undefined,
    sizeAfterClean: undefined
  }
}

// 添加日志清理记录
const handleAddCleanupRecord = () => {
  dialogTitle.value = '添加日志清理记录'
  isEdit.value = false
  resetFormData()
  dialogVisible.value = true
}

// 编辑日志清理记录
const handleEditCleanupRecord = (row: LogCleanupData) => {
  dialogTitle.value = '编辑日志清理记录'
  isEdit.value = true
  formData.value = { ...row }
  dialogVisible.value = true
}

// 取消操作
const handleCancel = () => {
  dialogVisible.value = false
  resetFormData()
  formRef.value?.clearValidate()
}

// 保存操作
const handleSave = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    formLoading.value = true

    const params = {
      ...formData.value,
      appId: props.appId!
    }

    let response
    if (isEdit.value) {
      response = await ApplicationApi.updateLogCleanRecord(params as any)
    } else {
      response = await ApplicationApi.saveLogCleanRecord(params as any)
    }

    if (response.success) {
      ElMessage.success(isEdit.value ? '修改成功' : '添加成功')
      dialogVisible.value = false
      resetFormData()
      await loadLogCleanupRecords()
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('操作失败')
  } finally {
    formLoading.value = false
  }
}

// 加载日志清理记录
const loadLogCleanupRecords = async (page: number = 1) => {
  if (!props.appId) return

  logCleanupLoading.value = true
  try {
    const response = await ApplicationApi.getLogCleanRecordPageInfo({
      appId: props.appId,
      pageNum: page,
      pageSize: logCleanupPagination.value.pageSize
    })

    if (response.success && response.data) {
      logCleanupData.value = response.data.records
      logCleanupPagination.value.current = response.data.current
      logCleanupPagination.value.total = response.data.total
    }
  } catch (error) {
    console.error('加载日志清理记录失败:', error)
  } finally {
    logCleanupLoading.value = false
  }
}

// 日志清理记录分页处理方法
const handleLogCleanupPageChange = (page: number) => {
  loadLogCleanupRecords(page)
}

const handleLogCleanupSizeChange = (size: number) => {
  logCleanupPagination.value.pageSize = size
  loadLogCleanupRecords(1)
}

// 监听appId变化，重新加载数据
watch(() => props.appId, async (newAppId, oldAppId) => {
  // 当appId变化时，清空日志清理记录数据
  if (newAppId !== oldAppId) {
    logCleanupData.value = []
    logCleanupPagination.value.current = 1
    logCleanupPagination.value.total = 0
  }

  if (newAppId && props.visible && !props.preloaded) {
    await loadDictOptions()
    await loadAppDetail()
    // 检查是否需要加载日志清理记录数据
    await checkAndLoadLogCleanupRecords()
  }
}, { immediate: true })

// 监听visible变化
watch(() => props.visible, async (newVisible) => {
  if (newVisible && props.appId) {
    // 清空日志清理记录数据
    logCleanupData.value = []
    logCleanupPagination.value.current = 1
    logCleanupPagination.value.total = 0

    // 无论是否预加载，都需要加载字典数据
    await loadDictOptions()

    // 如果数据已经预加载，直接使用预加载的数据
    if (props.preloaded && props.preloadedData) {
      updateDataFromProps()
    } else {
      // 常规加载流程
      await loadAppDetail()
    }

    // 检查是否需要加载日志清理记录数据
    await checkAndLoadLogCleanupRecords()
  }
}, { immediate: true })

// 监听tab切换
watch(() => activeTab.value, async (newTab) => {
  if (newTab === 'logCleanup' && props.appId && props.visible) {
    await loadLogCleanupRecords()
  }
})

// 检查是否需要加载日志清理记录数据
const checkAndLoadLogCleanupRecords = async () => {
  if (activeTab.value === 'logCleanup' && props.appId && props.visible) {
    await loadLogCleanupRecords()
  }
}

// 监听预加载数据变化
watch(() => props.preloadedData, (newData) => {
  if (props.visible && props.preloaded && newData) {
    updateDataFromProps()
  }
}, { deep: true })

// 从props更新数据的通用方法
function updateDataFromProps() {
  if (!props.preloadedData) return
  
  // 确保数据完整性
  if (props.preloadedData.detail) {
    detailData.value = props.preloadedData.detail
  }
  if (props.preloadedData.statusOptions && props.preloadedData.statusOptions.length > 0) {
    statusOptions.value = props.preloadedData.statusOptions
  }
  if (props.preloadedData.typeOptions && props.preloadedData.typeOptions.length > 0) {
    typeOptions.value = props.preloadedData.typeOptions
  }
}
</script>

<style scoped>
.application-detail-component {
  padding: 0;
}

/* 卡片样式 */
.detail-card {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
}

.card-header {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.header-title {
  color: #303133;
  position: relative;
  padding-left: 12px;
}

.header-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: #409EFF;
  border-radius: 2px;
}

/* 描述列表样式美化 */
:deep(.desc-label) {
  font-weight: 600 !important;
  color: #606266 !important;
  background-color: #f8f9fa !important;
  border-right: 2px solid #409EFF !important;
  width: 120px !important; /* 固定标签宽度 */
  text-align: center !important; /* 标签居中 */
}

:deep(.desc-content-center .el-descriptions__content) {
  text-align: center !important; /* 应用名称内容居中 */
}

.desc-value {
  color: #303133;
  font-weight: 500;
}

/* 选项卡样式 */
.detail-tabs {
  margin-top: 20px;
}

:deep(.el-tabs__header) {
  margin-bottom: 20px;
}

:deep(.el-tabs__item) {
  font-size: 14px;
  font-weight: 500;
}

:deep(.el-tabs__item.is-active) {
  color: #409EFF;
  font-weight: 600;
}

/* 表格头部操作区域 */
.table-header {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-start;
}

/* 分页容器 */
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 基本信息描述列表样式 */
:deep(.el-descriptions) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-descriptions__header) {
  margin-bottom: 16px;
}

:deep(.el-descriptions__label) {
  background: linear-gradient(135deg, #f5f7fa 0%, #e8ecef 100%) !important;
  border-right: 3px solid #409EFF !important;
  font-weight: 600 !important;
  color: #2c3e50 !important;
  font-size: 14px !important;
  text-align: center !important;
  letter-spacing: 0.5px !important;
  width: 100px !important;
  min-width: 100px !important;
  max-width: 100px !important;
}

:deep(.el-descriptions__content) {
  background-color: #ffffff !important;
  text-align: center !important;
  padding: 12px 16px !important;
  width: auto !important;
  word-break: break-all;
}

:deep(.el-descriptions__table) {
  border-collapse: separate;
  border-spacing: 0;
  table-layout: fixed !important;
  width: 100% !important;
}

:deep(.el-descriptions__table col) {
  width: auto;
}

:deep(.el-descriptions__table .el-descriptions__cell) {
  vertical-align: middle;
}

/* 更精确的列宽控制 */
:deep(.el-descriptions__table td:nth-child(1),
       .el-descriptions__table td:nth-child(3),
       .el-descriptions__table td:nth-child(5)) {
  width: 100px !important;
  min-width: 100px !important;
  max-width: 100px !important;
}

:deep(.el-descriptions__table td:nth-child(2),
       .el-descriptions__table td:nth-child(4),
       .el-descriptions__table td:nth-child(6)) {
  width: auto !important;
}

:deep(.el-descriptions__table td) {
  border: 1px solid #e8ecef;
  transition: background-color 0.2s ease;
}

:deep(.el-descriptions__table tr:hover td) {
  background-color: #f8fafe !important;
}

.field-text {
  color: #34495e;
  font-weight: 500;
  line-height: 1.5;
  word-break: break-all;
}

/* Git链接样式 */
.git-link {
  font-size: 13px;
  word-break: break-all;
  line-height: 1.4;
}

/* 标签样式优化 */
:deep(.el-descriptions__content .el-tag) {
  font-weight: 600;
  border-radius: 6px;
  padding: 4px 12px;
  font-size: 12px;
  letter-spacing: 0.3px;
}

/* 响应式布局 */
@media (max-width: 1200px) {
  :deep(.el-descriptions__label) {
    width: 90px !important;
    min-width: 90px !important;
    max-width: 90px !important;
  }
  
  :deep(.el-descriptions__table td:nth-child(1),
         .el-descriptions__table td:nth-child(3)) {
    width: 90px !important;
    min-width: 90px !important;
    max-width: 90px !important;
  }
}

@media (max-width: 768px) {
  :deep(.el-descriptions__label) {
    width: 80px !important;
    min-width: 80px !important;
    max-width: 80px !important;
    font-size: 13px !important;
    padding: 8px 8px !important;
  }
  
  :deep(.el-descriptions__table td:nth-child(1)) {
    width: 80px !important;
    min-width: 80px !important;
    max-width: 80px !important;
  }
  
  :deep(.el-descriptions__content) {
    padding: 8px 12px !important;
    font-size: 13px;
  }
  
  .git-link {
    font-size: 12px;
  }
  
  :deep(.el-descriptions__content .el-tag) {
    font-size: 11px !important;
    padding: 2px 8px !important;
  }
}

/* 表格样式 */
:deep(.el-table) {
  border-radius: 4px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 12px 0;
}

/* 表格居中样式 */
:deep(.center-table .el-table__header th) {
  text-align: center !important;
}

:deep(.center-table .el-table__body td) {
  text-align: center !important;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .application-detail-component {
    padding: 0;
  }

  :deep(.el-col) {
    margin-bottom: 16px;
  }
}
</style>