<template>
  <div class="module-list">
    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
              v-model="searchForm.appCode"
              placeholder="搜索模块标识"
              clearable
              @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="6">
          <el-input
              v-model="searchForm.appName"
              placeholder="搜索所属应用"
              clearable
              @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
          :data="tableData"
          v-loading="loading"
          stripe
          style="width: 100%"
      >
        <el-table-column prop="appCode" label="模块标识" min-width="150" />
        <el-table-column prop="appName" label="所属应用" min-width="150" />
        <el-table-column prop="apiCount" label="接口数量" width="100" align="center" />
        <el-table-column prop="deployPort" label="部署端口" width="100" align="center" />
        <el-table-column prop="deployPath" label="部署路径" min-width="200" show-overflow-tooltip />
        <el-table-column prop="serverNames" label="部署服务器" min-width="200" show-overflow-tooltip />
        <el-table-column prop="updateTime" label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.updateTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="info" size="small" @click="handleView(row)">
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
            v-model:current-page="pagination.pageNum"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 查看详情对话框 -->
    <el-dialog
        v-model="dialogVisible"
        title="模块详情"
        width="800px"
        @close="handleDialogClose"
    >
      <!-- 基本信息 -->
      <el-descriptions :column="2" border class="module-basic-info">
        <el-descriptions-item label="模块标识">{{ currentModule.appCode }}</el-descriptions-item>
        <el-descriptions-item label="所属应用">{{ currentModule.appName }}</el-descriptions-item>
        <el-descriptions-item label="接口数量">{{ currentModule.apiCount }}</el-descriptions-item>
        <el-descriptions-item label="部署端口">{{ currentModule.deployPort }}</el-descriptions-item>
        <el-descriptions-item label="部署路径" :span="2">{{ currentModule.deployPath }}</el-descriptions-item>
        <el-descriptions-item label="部署服务器" :span="2">{{ currentModule.serverNames }}</el-descriptions-item>
        <el-descriptions-item label="更新时间" :span="2">{{ formatDate(currentModule.updateTime) }}</el-descriptions-item>
      </el-descriptions>

      <!-- Tab页 -->
      <el-tabs v-model="activeTab" class="module-detail-tabs">
        <!-- 实例Tab -->
        <el-tab-pane label="实例" name="instances">
          <el-table
              :data="instanceData"
              v-loading="instanceLoading"
              stripe
              style="width: 100%"
          >
            <el-table-column prop="privateIp" label="私网IP" width="150" />
            <el-table-column prop="publicIp" label="公网IP" width="150" />
            <el-table-column prop="jvmParams" label="JVM参数" min-width="200" show-overflow-tooltip />
          </el-table>
        </el-tab-pane>

        <!-- 依赖Tab -->
        <el-tab-pane label="依赖" name="dependencies">
          <el-table
              :data="dependencyData"
              v-loading="dependencyLoading"
              stripe
              style="width: 100%"
          >
            <el-table-column prop="groupId" label="groupID" width="200" />
            <el-table-column prop="artifactId" label="artifictID" width="200" />
            <el-table-column prop="version" label="version" width="150" />
          </el-table>
        </el-tab-pane>
      </el-tabs>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'

// 搜索表单
const searchForm = reactive({
  appCode: '',
  appName: ''
})

// 表格数据
interface ModuleInfo {
  moduleId: number
  appCode: string
  appName: string
  apiCount: number
  deployPort: number
  deployPath: string
  serverNames: string
  updateTime: string
}

// 模块服务器关系数据
interface ModuleServerRelation {
  privateIp: string
  publicIp: string
  jvmParams: string
}

// 模块依赖数据
interface ModuleDependency {
  groupId: string
  artifactId: string
  version: string
}

let tableData = reactive<ModuleInfo[]>([])
const loading = ref(false)

// 分页
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 对话框
const dialogVisible = ref(false)
const activeTab = ref('instances')

// 当前查看的模块
const currentModule = reactive<ModuleInfo>({
  moduleId: 0,
  appCode: '',
  appName: '',
  apiCount: 0,
  deployPort: 0,
  deployPath: '',
  serverNames: '',
  updateTime: ''
})

// Tab页数据
const instanceData = reactive<ModuleServerRelation[]>([])
const dependencyData = reactive<ModuleDependency[]>([])
const instanceLoading = ref(false)
const dependencyLoading = ref(false)



// 格式化日期
const formatDate = (date: string): string => {
  return new Date(date).toLocaleString('zh-CN')
}

// 搜索
const handleSearch = () => {
  pagination.pageNum = 1
  loadData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    appCode: '',
    appName: ''
  })
  handleSearch()
}

// 加载实例数据
const loadInstanceData = async (moduleId: number) => {
  instanceLoading.value = true
  try {
    const response = await fetch(`/api/cmdb/module-server-relation/by-sub-app/${moduleId}`)
    const result = await response.json()

    if (result.success) {
      instanceData.splice(0, instanceData.length, ...result.data)
    } else {
      ElMessage.error(result.message || '获取实例数据失败')
    }
  } catch (error) {
    console.error('加载实例数据失败:', error)
    ElMessage.error('网络错误，请稍后重试')
  } finally {
    instanceLoading.value = false
  }
}

// 加载依赖数据
const loadDependencyData = async (moduleId: number) => {
  dependencyLoading.value = true
  try {
    const response = await fetch(`/api/cmdb/module-dependency/by-sub-app/${moduleId}`)
    const result = await response.json()

    if (result.success) {
      dependencyData.splice(0, dependencyData.length, ...result.data)
    } else {
      ElMessage.error(result.message || '获取依赖数据失败')
    }
  } catch (error) {
    console.error('加载依赖数据失败:', error)
    ElMessage.error('网络错误，请稍后重试')
  } finally {
    dependencyLoading.value = false
  }
}

// 查看
const handleView = (row: ModuleInfo) => {
  Object.assign(currentModule, row)
  activeTab.value = 'instances'
  dialogVisible.value = true

  // 加载tab页数据
  loadInstanceData(row.moduleId)
  loadDependencyData(row.moduleId)
}

// 对话框关闭处理
const handleDialogClose = () => {
  // 清空数据
  instanceData.splice(0, instanceData.length)
  dependencyData.splice(0, dependencyData.length)
  activeTab.value = 'instances'
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.pageNum = 1
  loadData()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.pageNum = page
  loadData()
}

// 加载数据
const loadData = async () => {
  loading.value = true

  try {
    // 构建查询参数
    const params = new URLSearchParams()
    params.append('page', pagination.pageNum.toString())
    params.append('pageSize', pagination.pageSize.toString())

    if (searchForm.appCode) {
      params.append('appCode', searchForm.appCode)
    }
    if (searchForm.appName) {
      params.append('appName', searchForm.appName)
    }

    // 调用API
    const response = await fetch('/api/cmdb/system-app-info/module/page?' + params.toString())
    const result = await response.json()

    if (result.success) {
      tableData.splice(0, tableData.length, ...result.data.records)
      pagination.total = result.data.total
    } else {
      ElMessage.error(result.message || '获取数据失败')
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('网络错误，请稍后重试')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.module-list {
  padding: 0;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 环境配置样式 */
.environment-config {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.env-item {
  margin-bottom: 15px;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: white;
}

.env-item:last-child {
  margin-bottom: 10px;
}

/* Swagger配置样式 */
.swagger-config {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.swagger-item {
  margin-bottom: 15px;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: white;
}

.swagger-item:last-child {
  margin-bottom: 10px;
}

/* 模块详情样式 */
.module-basic-info {
  margin-bottom: 20px;
}

.module-detail-tabs {
  margin-top: 20px;
}

.module-detail-tabs .el-table {
  margin-top: 10px;
}
</style>
