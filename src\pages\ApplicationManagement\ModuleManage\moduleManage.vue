<template>
  <div class="module-list">
    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
              v-model="searchForm.appCode"
              placeholder="搜索模块标识"
              clearable
              @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="6">
          <el-input
              v-model="searchForm.appName"
              placeholder="搜索所属应用"
              clearable
              @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
          :data="tableData"
          v-loading="loading"
          stripe
          style="width: 100%"
      >
        <el-table-column prop="appCode" label="模块标识" min-width="150" />
        <el-table-column prop="appName" label="所属应用" min-width="150" />
        <el-table-column prop="apiCount" label="接口数量" width="100" align="center" />
        <el-table-column prop="deployPort" label="部署端口" width="100" align="center" />
        <el-table-column prop="deployPath" label="部署路径" min-width="200" show-overflow-tooltip />
        <el-table-column prop="serverNames" label="部署服务器" min-width="200" show-overflow-tooltip />
        <el-table-column prop="updateTime" label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.updateTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="info" size="small" @click="handleView(row)">
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 查看详情对话框 -->
    <el-dialog
        v-model="dialogVisible"
        title="模块详情"
        width="600px"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="模块标识">{{ currentModule.appCode }}</el-descriptions-item>
        <el-descriptions-item label="所属应用">{{ currentModule.appName }}</el-descriptions-item>
        <el-descriptions-item label="接口数量">{{ currentModule.apiCount }}</el-descriptions-item>
        <el-descriptions-item label="部署端口">{{ currentModule.deployPort }}</el-descriptions-item>
        <el-descriptions-item label="部署路径" :span="2">{{ currentModule.deployPath }}</el-descriptions-item>
        <el-descriptions-item label="部署服务器" :span="2">{{ currentModule.serverNames }}</el-descriptions-item>
        <el-descriptions-item label="更新时间" :span="2">{{ formatDate(currentModule.updateTime) }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'

// 搜索表单
const searchForm = reactive({
  appCode: '',
  appName: ''
})

// 表格数据
interface ModuleInfo {
  moduleId: number
  appCode: string
  appName: string
  apiCount: number
  deployPort: number
  deployPath: string
  serverNames: string
  updateTime: string
}

let tableData = reactive<ModuleInfo[]>([])
const loading = ref(false)

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 对话框
const dialogVisible = ref(false)

// 当前查看的模块
const currentModule = reactive<ModuleInfo>({
  moduleId: 0,
  appCode: '',
  appName: '',
  apiCount: 0,
  deployPort: 0,
  deployPath: '',
  serverNames: '',
  updateTime: ''
})



// 格式化日期
const formatDate = (date: string): string => {
  return new Date(date).toLocaleString('zh-CN')
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    appCode: '',
    appName: ''
  })
  handleSearch()
}

// 查看
const handleView = (row: ModuleInfo) => {
  Object.assign(currentModule, row)
  dialogVisible.value = true
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  loadData()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadData()
}

// 加载数据
const loadData = async () => {
  loading.value = true

  try {
    // 构建查询参数
    const params = new URLSearchParams()
    params.append('page', pagination.page.toString())
    params.append('pageSize', pagination.pageSize.toString())

    if (searchForm.appCode) {
      params.append('appCode', searchForm.appCode)
    }
    if (searchForm.appName) {
      params.append('appName', searchForm.appName)
    }

    // 调用API
    const response = await fetch('/api/cmdb/system-app-info/module/page?' + params.toString())
    const result = await response.json()

    if (result.success) {
      tableData.splice(0, tableData.length, ...result.data.records)
      pagination.total = result.data.total
    } else {
      ElMessage.error(result.message || '获取数据失败')
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    // 模拟数据用于开发测试
    const mockData: ModuleInfo[] = [
      {
        moduleId: 1,
        appCode: 'pams-ui',
        appName: 'PAMS前端应用',
        apiCount: 25,
        deployPort: 8080,
        deployPath: '/pams-ui',
        serverNames: 'server-01, server-02',
        updateTime: '2024-01-15 10:30:00'
      },
      {
        moduleId: 2,
        appCode: 'pams-api',
        appName: 'PAMS后端API',
        apiCount: 156,
        deployPort: 8081,
        deployPath: '/api',
        serverNames: 'server-03, server-04, server-05',
        updateTime: '2024-01-14 16:45:00'
      },
      {
        moduleId: 3,
        appCode: 'pams-gateway',
        appName: 'PAMS网关服务',
        apiCount: 8,
        deployPort: 8082,
        deployPath: '/gateway',
        serverNames: 'server-06',
        updateTime: '2024-01-13 09:15:00'
      }
    ]

    // 应用搜索过滤
    let filteredData = mockData
    if (searchForm.appCode) {
      filteredData = filteredData.filter(item =>
        item.appCode.toLowerCase().includes(searchForm.appCode.toLowerCase())
      )
    }
    if (searchForm.appName) {
      filteredData = filteredData.filter(item =>
        item.appName.toLowerCase().includes(searchForm.appName.toLowerCase())
      )
    }

    // 分页处理
    const start = (pagination.page - 1) * pagination.pageSize
    const end = start + pagination.pageSize
    const pageData = filteredData.slice(start, end)

    tableData.splice(0, tableData.length, ...pageData)
    pagination.total = filteredData.length
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.module-list {
  padding: 0;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 环境配置样式 */
.environment-config {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.env-item {
  margin-bottom: 15px;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: white;
}

.env-item:last-child {
  margin-bottom: 10px;
}

/* Swagger配置样式 */
.swagger-config {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.swagger-item {
  margin-bottom: 15px;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: white;
}

.swagger-item:last-child {
  margin-bottom: 10px;
}
</style>
