<template>
  <div class="module-list">
    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
              v-model="searchForm.keyword"
              placeholder="搜索模块名称"
              clearable
              @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.applicationId" placeholder="所属应用" clearable>
            <el-option />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
          :data="tableData"
          v-loading="loading"
          stripe
          style="width: 100%"
      >
        <el-table-column prop="name" label="模块名称" min-width="150" />
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="owner" label="负责人" width="100" />
        <el-table-column prop="applicationName" label="所属应用" width="120" />
        <el-table-column prop="companyName" label="所属公司" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="info" size="small" @click="handleView(row)">
              查看
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
        v-model="dialogVisible"
        :title="dialogTitle"
        width="800px"
        @close="handleDialogClose"
    >
      <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="100px"
      >
        <el-form-item label="模块名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入模块名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
              v-model="form.description"
              type="textarea"
              :rows="3"
              placeholder="请输入模块描述"
          />
        </el-form-item>
        <el-form-item label="负责人" prop="owner">
          <el-input v-model="form.owner" placeholder="请输入负责人" />
        </el-form-item>
        <el-form-item label="所属应用" prop="applicationId">
          <el-select v-model="form.applicationId" placeholder="请选择所属应用" style="width: 100%">
          </el-select>
        </el-form-item>
        <el-form-item label="所属公司" prop="companyId">
          <el-select v-model="form.companyId" placeholder="请选择所属公司" style="width: 100%">
            <el-option
                v-for="company in companies"
                :key="company.id"
                :label="company.name"
                :value="company.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="模块状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择模块状态" style="width: 100%">
            <el-option label="启用" value="enabled" />
            <el-option label="禁用" value="disabled" />
            <el-option label="维护中" value="maintenance" />
          </el-select>
        </el-form-item>

        <el-form-item label="环境域名配置">
          <div class="environment-config">
            <div v-for="(env, index) in form.environments" :key="index" class="env-item">
              <el-row :gutter="15">
                <el-col :span="3">
                  <el-select v-model="env.environment" placeholder="环境" style="width: 100%">
                    <el-option label="开发环境" value="development" />
                    <el-option label="测试环境" value="testing" />
                    <el-option label="生产环境" value="production" />
                  </el-select>
                </el-col>
                <el-col :span="8">
                  <el-input v-model="env.domain" placeholder="域名，如：https://api.example.com" />
                </el-col>
                <el-col :span="6">
                  <el-input v-model="env.description" placeholder="描述" />
                </el-col>
                <el-col :span="3">
                  <el-checkbox v-model="env.isDefault">默认</el-checkbox>
                </el-col>
                <el-col :span="2">
                  <el-button type="danger" size="small" @click="removeEnvironment(index)" :disabled="form.environments.length <= 1">
                    删除
                  </el-button>
                </el-col>
              </el-row>
            </div>
            <el-button type="primary" size="small" @click="addEnvironment">
              <el-icon><Plus /></el-icon>
              添加环境
            </el-button>
          </div>
        </el-form-item>

        <el-form-item label="Swagger配置">
          <div class="swagger-config">
            <div v-for="(swagger, index) in form.swaggerConfigs" :key="index" class="swagger-item">
              <el-row :gutter="15">
                <el-col :span="6">
                  <el-input v-model="swagger.name" placeholder="配置名称" />
                </el-col>
                <el-col :span="12">
                  <el-input v-model="swagger.url" placeholder="Swagger文档URL" />
                </el-col>
                <el-col :span="4">
                  <el-input v-model="swagger.description" placeholder="描述" />
                </el-col>
                <el-col :span="1">
                  <el-checkbox v-model="swagger.isActive">启用</el-checkbox>
                </el-col>
                <el-col :span="1">
                  <el-button type="danger" size="small" @click="removeSwaggerConfig(index)">
                    删除
                  </el-button>
                </el-col>
              </el-row>
            </div>
            <el-button type="success" size="small" @click="addSwaggerConfig">
              <el-icon><Plus /></el-icon>
              添加Swagger配置
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Search, Refresh, Plus, Link, Document } from '@element-plus/icons-vue'

// 搜索表单
const searchForm = reactive({
  keyword: '',
  applicationId: '',
  status: '',
  companyId: ''
})

// 表格数据
let tableData = reactive([])
const loading = ref(false)

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 对话框
const dialogVisible = ref(false)
const dialogTitle = ref('新增模块')
const submitLoading = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const form = reactive({
  id: '',
  name: '',
  description: '',
  owner: '',
  applicationId: '',
  companyId: '',
  status: 'enabled',
  environments: [
    {
      environment: 'production',
      domain: '',
      description: '生产环境',
      isDefault: true
    }
  ],
  swaggerConfigs: [] as any[]
})

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入模块名称', trigger: 'blur' }
  ],
  owner: [
    { required: true, message: '请输入负责人', trigger: 'blur' }
  ],
  applicationId: [
    { required: true, message: '请选择所属应用', trigger: 'change' }
  ],
  companyId: [
    { required: true, message: '请选择所属公司', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择模块状态', trigger: 'change' }
  ],
  environments: [
    {
      validator: (rule, value, callback) => {
        if (!value || value.length === 0) {
          callback(new Error('至少需要配置一个环境域名'))
          return
        }

        for (const env of value) {
          if (!env.domain) {
            callback(new Error('请填写所有环境的域名'))
            return
          }
          if (!/^https?:\/\/.+/.test(env.domain)) {
            callback(new Error('请输入有效的域名，如：https://api.example.com'))
            return
          }
        }

        const defaultCount = value.filter((env: any) => env.isDefault).length
        if (defaultCount === 0) {
          callback(new Error('请设置一个默认环境'))
          return
        }
        if (defaultCount > 1) {
          callback(new Error('只能设置一个默认环境'))
          return
        }

        callback()
      },
      trigger: 'blur'
    }
  ],
  swaggerConfigs: [
    {
      validator: (rule, value, callback) => {
        if (!value || value.length === 0) {
          callback() // swagger配置是可选的
          return
        }

        for (const swagger of value) {
          if (swagger.name && !swagger.url) {
            callback(new Error('请填写Swagger配置的URL'))
            return
          }

        }

        callback()
      },
      trigger: 'blur'
    }
  ]
}

// 获取应用和公司列表
const applications = reactive([])
const companies = reactive([])

// 获取状态类型
const getStatusType = (status: string): string => {
  const typeMap: Record<string, string> = {
    enabled: 'success',
    disabled: 'info',
    maintenance: 'warning'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string): string => {
  const textMap: Record<string, string> = {
    enabled: '启用',
    disabled: '禁用',
    maintenance: '维护中'
  }
  return textMap[status] || status
}

// 获取环境文本
const getEnvironmentText = (environment: string): string => {
  const textMap: Record<string, string> = {
    development: '开发环境',
    testing: '测试环境',
    staging: '预发环境',
    production: '生产环境'
  }
  return textMap[environment] || environment
}

// 格式化日期
const formatDate = (date: string): string => {
  return new Date(date).toLocaleString('zh-CN')
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    applicationId: '',
    status: '',
    companyId: ''
  })
  handleSearch()
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增模块'
  Object.assign(form, {
    id: '',
    name: '',
    description: '',
    owner: '',
    applicationId: '',
    companyId: '',
    status: 'enabled',
    environments: [
      {
        environment: 'production',
        domain: '',
        description: '生产环境',
        isDefault: true
      }
    ],
    swaggerConfigs: []
  })
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row: any) => {
  dialogTitle.value = '编辑模块'
  Object.assign(form, row)
  dialogVisible.value = true
}

// 查看
const handleView = (row: any) => {
  ElMessage.info('查看功能开发中...')
}

// 删除
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
        `确定要删除模块"${row.name}"吗？`,
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
    )

    // 模拟删除
  } catch {
    // 用户取消删除
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitLoading.value = true

    // 模拟提交
    await new Promise(resolve => setTimeout(resolve, 1000))

    const application = applications.value.find(app => app.id === form.applicationId)
    const company = companies.value.find(comp => comp.id === form.companyId)

    if (form.id) {
      // 编辑
      const index = tableData.value.findIndex(item => item.id === form.id)
      if (index > -1) {
        Object.assign(tableData.value[index], {
          ...form,
          applicationName: application?.name || '',
          companyName: company?.name || '',
          updatedAt: new Date().toISOString()
        })
      }
      ElMessage.success('编辑成功')
    } else {
      // 新增

      ElMessage.success('新增成功')
    }

    dialogVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitLoading.value = false
  }
}

// 对话框关闭
const handleDialogClose = () => {
  formRef.value?.resetFields()
  form.swaggerConfigs = []
}

// 添加环境配置
const addEnvironment = () => {
  form.environments.push({
    environment: 'development',
    domain: '',
    description: '',
    isDefault: false
  })
}

// 删除环境配置
const removeEnvironment = (index: number) => {
  form.environments.splice(index, 1)
}

// 添加Swagger配置
const addSwaggerConfig = () => {
  form.swaggerConfigs.push({
    id: Date.now().toString(),
    name: '',
    url: '',
    description: '',
    environment: undefined,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  })
}

// 删除Swagger配置
const removeSwaggerConfig = (index: number) => {
  form.swaggerConfigs.splice(index, 1)
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  loadData()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadData()
}

// 加载数据
const loadData = () => {
  loading.value = true

  // 模拟API调用
  setTimeout(() => {
    let filteredData = []

    // 关键词搜索
    if (searchForm.keyword) {
      filteredData = filteredData.filter(item =>
          item.name.includes(searchForm.keyword) ||
          item.description?.includes(searchForm.keyword)
      )
    }

    // 应用筛选
    if (searchForm.applicationId) {
      filteredData = filteredData.filter(item => item.applicationId === searchForm.applicationId)
    }

    // 状态筛选
    if (searchForm.status) {
      filteredData = filteredData.filter(item => item.status === searchForm.status)
    }

    // 公司筛选
    if (searchForm.companyId) {
      filteredData = filteredData.filter(item => item.companyId === searchForm.companyId)
    }

    // 分页
    const start = (pagination.page - 1) * pagination.pageSize
    const end = start + pagination.pageSize

    pagination.total = filteredData.length

    loading.value = false
  }, 500)
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.module-list {
  padding: 0;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 环境配置样式 */
.environment-config {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.env-item {
  margin-bottom: 15px;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: white;
}

.env-item:last-child {
  margin-bottom: 10px;
}

/* Swagger配置样式 */
.swagger-config {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.swagger-item {
  margin-bottom: 15px;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: white;
}

.swagger-item:last-child {
  margin-bottom: 10px;
}
</style>
