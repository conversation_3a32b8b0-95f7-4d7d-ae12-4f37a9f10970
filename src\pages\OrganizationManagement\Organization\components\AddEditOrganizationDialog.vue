<template>
  <el-dialog v-model="visible" :title="isEdit ? '编辑机构' : '新增机构'" width="600px" @close="handleClose">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="机构名称" prop="organizationName">
        <el-input v-model="form.organizationName" placeholder="请输入机构名称" />
      </el-form-item>
      <el-form-item label="机构类型" prop="organizationType">
        <el-select v-model="form.organizationType" placeholder="请选择机构类型" style="width: 100%">
          <el-option v-for="item in organizationTypeOptions" :key="item.value" :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入公司描述"/>
      </el-form-item>
      <el-form-item label="联系人" prop="contactPerson">
        <el-input v-model="form.contactPerson" placeholder="请输入联系人姓名" />
      </el-form-item>
      <el-form-item label="联系电话" prop="contactPhone">
        <el-input v-model="form.contactPhone" placeholder="请输入联系电话" />
      </el-form-item>
      <el-form-item label="联系邮箱" prop="contactEmail">
        <el-input v-model="form.contactEmail" placeholder="请输入联系邮箱" />
      </el-form-item>
      <el-form-item label="合作状态" prop="cooperativeStatus">
        <el-select v-model="form.cooperativeStatus" placeholder="请选择合作状态" style="width: 100%">
          <el-option v-for="item in cooperativeStatusOptions" :key="item.value" :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading"> 确定</el-button>
        <el-button @click="handleClose">取消</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { getOrganizationInfo, saveOrganization, updateOrganization } from '@/api/OrganizationManagement'

interface Props {
  modelValue: boolean
  editId?: number
  organizationTypeOptions: Array<{label: string, value: string}>
  cooperativeStatusOptions: Array<{label: string, value: string}>
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  editId: undefined,
  organizationTypeOptions: () => [],
  cooperativeStatusOptions: () => []
})

const emit = defineEmits<Emits>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const isEdit = computed(() => !!props.editId)

const formRef = ref<FormInstance>()
const submitLoading = ref(false)

// 表单数据
const form = reactive<any>({
  organizationName: '',
  organizationType: '',
  description: '',
  contactPerson: '',
  contactPhone: '',
  contactEmail: '',
  cooperativeStatus: ''
})

// 表单验证规则
const rules: FormRules = {
  organizationName: [
    { required: true, message: '请输入机构名称', trigger: 'blur' }
  ],
  organizationType: [
    { required: true, message: '请选择机构类型', trigger: 'change' }
  ],
  contactPerson: [
    { required: true, message: '请输入联系人姓名', trigger: 'blur' }
  ],
  contactPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' }
  ],
  contactEmail: [
    { required: true, message: '请输入联系邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  cooperativeStatus: [
    { required: true, message: '请选择合作状态', trigger: 'change' }
  ]
}

// 加载编辑数据
const loadEditData = async () => {
  if (!props.editId) return
  try {
    await getOrganizationInfo(props.editId).then(res => {
      if (res.code === '0000') {
        Object.assign(form, res.data)
      }
    })
  } catch (error) {
    console.error('加载编辑数据失败:', error)
    ElMessage.error('加载数据失败')
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    organizationName: '',
    organizationType: '',
    description: '',
    contactPerson: '',
    contactPhone: '',
    contactEmail: '',
    cooperativeStatus: ''
  })
  formRef.value?.resetFields()
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitLoading.value = true
    if (isEdit.value) {
      // 编辑
      const updateParams: any = {
        id: props.editId!,
        ...form
      }
      await updateOrganization(updateParams).then(res => {
        if (res.code === '0000') {
          ElMessage.success('编辑成功')
          emit('success')
          handleClose()
        } else {
          ElMessage.error({message: res.message ,type: 'error'})
        }
      })
    } else {
      // 新增
      const saveParams: any = {
        ...form
      }
      await saveOrganization(saveParams).then(res => {
        if (res.code === '0000') {
          ElMessage.success('新增成功')
          emit('success')
          handleClose()
        } else {
          ElMessage.error({message: res.message ,type: 'error'})
        }
      })
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitLoading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  resetForm()
}

// 监听编辑ID变化
watch(() => props.editId, (newId) => {
  if (newId) {
    loadEditData()
  } else {
    resetForm()
  }
})

// 监听对话框显示状态
watch(visible, (newVisible) => {
  if (newVisible) {
    if (props.editId) {
      loadEditData()
    } else {
      resetForm()
    }
  }
})
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>