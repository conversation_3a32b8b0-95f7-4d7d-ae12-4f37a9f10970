<template>
  <div class="organization-list">
    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <el-row :gutter="20">
        <el-col :span="5">
          <el-input v-model="searchForm.organizationName" placeholder="搜索机构名称" clearable>
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="5">
          <el-select v-model="searchForm.organizationType" placeholder="机构类型" clearable>
            <el-option v-for="item in organizationTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-col>
        <el-col :span="5">
          <el-select v-model="searchForm.cooperativeStatus" placeholder="合作状态" clearable>
            <el-option v-for="item in cooperativeStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-col>
        <el-col :span="5">
          <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
          <el-button :icon="Refresh" @click="handleReset">重置</el-button>
        </el-col>
        <el-col :span="4" style="text-align: right">
          <el-button type="success" :icon="Plus" @click="handleAdd">新增机构</el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table :data="tableData" v-loading="loading" stripe style="width: 100%">
        <el-table-column prop="organizationName" label="组/公司" width="150" />
        <el-table-column prop="organizationType" label="类型" width="150">
          <template #default="{ row }">
            <el-tag :type="getOrganizationTypeTagType(row.organizationType)">
              {{ getOrganizationTypeText(row.organizationType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="contactPerson" label="联系人" width="120" />
        <el-table-column prop="contactPhone" label="联系电话" width="120" />
        <el-table-column prop="contactEmail" label="联系邮箱" min-width="150" show-overflow-tooltip />
        <el-table-column prop="cooperativeStatus" label="合作状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getCooperativeStatusTagType(row.cooperativeStatus)">
              {{ getCooperativeStatusText(row.cooperativeStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small"  @click="handleEdit(row)">编辑</el-button>
            <el-button type="danger" size="small"  @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
            v-model:current-page="pagination.pageNum"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
      </div>

    </el-card>

    

    <!-- 新增/编辑对话框 -->
    <AddEditOrganizationDialog 
      v-model="dialogVisible" 
      :edit-id="editId" 
      :organization-type-options="organizationTypeOptions"
      :cooperative-status-options="cooperativeStatusOptions"
      @success="handleFormSuccess" 
    />

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus } from '@element-plus/icons-vue'
import { getOrganizationPage, deleteOrganization } from '@/api/OrganizationManagement'
import { dictList } from '@/api/CommApi'
import AddEditOrganizationDialog from './components/AddEditOrganizationDialog.vue'

// 搜索表单
const searchForm = reactive({
  organizationName: '',
  organizationType: '',
  cooperativeStatus: '',
  orderFields: 'createTime',
  orderRules: 'desc'

})

// 表格数据
const tableData = ref([])
const loading = ref(false)
// 分页
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 对话框
const dialogVisible = ref(false)
const editId = ref<number>()

// 字典选项
const organizationTypeOptions = ref<Array<{label: string, value: string}>>([])
const cooperativeStatusOptions = ref<Array<{label: string, value: string}>>([])

// 加载数据
const loadData = async () => {
    const params = {
      pageNum: pagination.pageNum || 1,
      pageSize: pagination.pageSize,
      ...searchForm
    }
    loading.value = true
    await getOrganizationPage(params).then((res: any) => {
      loading.value = false
      if (res.code === '0000') {
        tableData.value = res.data.records || []
        pagination.total = res.data.total || 0
      } else {
        ElMessage.error({message: res.message, type: 'error'})
      }
    })
}

// 加载字典数据:合作状态和机构类型
const loadDictData = async () => {
  try {
    const [orgTypeRes, statusRes] = await Promise.all([
      dictList('organization_type'),
      dictList('cooperative_status')
    ])
    organizationTypeOptions.value = orgTypeRes.data;
    cooperativeStatusOptions.value = statusRes.data;
  } catch (error) {
    console.error('加载字典数据失败:', error)
  }
}

// 获取机构类型标签类型
const getOrganizationTypeTagType = (type: string): string => {
  const typeMap: Record<string, string> = {
    'internal_company': 'primary',
    'partner_company': 'success',
    'third_party_company': 'warning'
  }
  return typeMap[type] || 'info'
}

// 获取机构类型文本
const getOrganizationTypeText = (type: string): string => {
  const option = organizationTypeOptions.value.find(item => item.value === type)
  return option?.label || type
}

// 获取合作状态标签类型
const getCooperativeStatusTagType = (status: string): string => {
  const typeMap: Record<string, string> = {
    'effective': 'success',
    'termination': 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取合作状态文本
const getCooperativeStatusText = (status: string): string => {
  const option = cooperativeStatusOptions.value.find(item => item.value === status)
  return option?.label || status
}

// 搜索
const handleSearch = () => {
  pagination.pageNum = 1
  loadData()
}

// 重置
const handleReset = () => {
  searchForm.organizationName = ''
  searchForm.organizationType = ''
  searchForm.cooperativeStatus = ''
  searchForm.orderFields = 'createTime'
  searchForm.orderRules = 'desc'
  handleSearch()
}

// 新增
const handleAdd = () => {
  editId.value = undefined
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row: any) => {
  editId.value = row.id
  dialogVisible.value = true
}

// 删除
const handleDelete = async (row: any) => {
   await ElMessageBox.confirm(
        `确定要删除机构"${row.organizationName}"吗？`, '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
    )
    const res = await deleteOrganization(row.id)
    if (res.code === '0000') {
      ElMessage.success('删除成功')
      loadData()
    } else {
      ElMessage.error(res.message || '删除失败')
    }
}

// 表单成功回调
const handleFormSuccess = () => {
  loadData()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.pageNum = 1
  loadData()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.pageNum = page
  loadData()
}

onMounted(() => {
  loadDictData()
  loadData()
})
</script>

<style scoped>
.organization-list {
  padding: 0;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>