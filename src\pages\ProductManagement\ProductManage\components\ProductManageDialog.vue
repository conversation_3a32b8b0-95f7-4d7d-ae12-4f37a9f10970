<!-- 新增编辑产品弹框 -->
<template>
  <div>
    <el-dialog :title="'产品编辑'" v-model="state.visible" width="500" class="dialog-input" @close="resetDialog()" :close-on-click-modal="false">
      <el-form :inline="true" :model="state.form" :rules="rules" ref="ruleFormRef" label-width="140">
        <el-form-item label="产品名称" prop="systemName" >
          <el-input v-model.trim="state.form.systemName" disabled placeholder="请输入" />
        </el-form-item>
        <el-form-item label="所属组/公司" prop="organizationId">
          <el-select v-model="state.form.organizationId" >
          <el-option v-for="item in state.organizationOptions" :key="item.id" :label="item.organizationName" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <div class="tc mt20">
        <el-button type="primary" @click="submitForm(ruleFormRef)">保存</el-button>
        <el-button @click="$emit('update:dialogVisible', false)">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch, ref } from 'vue'
import { type FormInstance, type FormRules, ElMessage } from 'element-plus'
import {updateProduct} from "@/api/ProductManagement";
// 定义要发送的emit事件
let $emit = defineEmits(['update:dialogVisible', 'callback'])
// 参数管理
const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  dialogForm: {
    type: Object,
    default: () => {
      return {}
    }
  },
  organizationOptions: {
    type:  Array<{organizationName: string, id: string}>,
    default: () => {
      return {}
    }
  }
})
const state = reactive({
  visible: false,
  form: {
    id: '',
    organizationId: '',
    systemName: ''
  },
  organizationOptions:  [{}] as Array<{organizationName: string, id: string}>
})

// form 表单校验
const ruleFormRef = ref<FormInstance>()
const rules = reactive<FormRules>({
  keyName: [{ required: true, message: '请输入字典编码', trigger: 'blur' }],
  keyLabel: [{ required: true, message: '请输入字典名称', trigger: 'blur' }]
})

// 弹窗-保存
const submitForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.validate((valid) => {
    if (valid) {
      updateProduct(state.form).then((res: any) => {
        if (res.code === '0000') {
          ElMessage({type: 'success', message: '操作成功'})
          $emit('callback')
          $emit('update:dialogVisible', false)
        } else {
          ElMessage({ message: res.message, type: 'error' })
        }
      })
    }
  })
}

// 重置弹窗
const resetDialog = () => {
  state.form = {
    id: '',
    organizationId: '',
    systemName: ''
  }
  $emit('update:dialogVisible', false)
}

watch(
    () => props.dialogVisible,
    (newVal) => {
      state.visible = newVal
      if (newVal) {
        state.form = {...state.form, ...props.dialogForm}
        state.organizationOptions = {...state.organizationOptions, ...props.organizationOptions}
      }
    }
)
</script>

<style lang="scss" scoped>
.el-select__wrapper {
  width: 200px;
}
.dialog-input .el-input {
  width: 260px;
}
.dialog-input ::deep(.el-textarea__inner) {
  width: 260px;
}
.dialog-input .el-select__wrapper {
  width: 260px;
}
.el-message-box__container {
  align-items: normal;
}
.dialog-title {
  margin-bottom: 10px;
  text-align: center;
  font-size: 17px;
  font-weight: bold;
}
.el-select {
  width: 220px;
}
</style>