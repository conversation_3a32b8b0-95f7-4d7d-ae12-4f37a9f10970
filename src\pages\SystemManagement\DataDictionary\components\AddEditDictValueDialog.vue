<!-- 新增编辑字典值弹框 -->
 <template>
  <div>
    <el-dialog :title="state.form.id ? '编辑' : '添加'" v-model="state.visible" width="500" class="dialog-input" @close="resetDialog()" :close-on-click-modal="false">
      <el-form :inline="true" :model="state.form" :rules="rules" ref="ruleFormRef" label-width="140">
        <el-form-item label="字典项" prop="label">
          <el-input v-model="state.form.label" placeholder="请输入"/>
        </el-form-item>
        <el-form-item label="字典值" prop="value">
          <el-input v-model="state.form.value" placeholder="请输入"/>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="state.form.description" style="width: 260px;" type="textarea" :rows="3" :autosize="{ minRows: 3, maxRows: 10}" placeholder="请输入" />
        </el-form-item>
      </el-form>
      <div class="tc mt20">
        <el-button type="primary" @click="submitForm(ruleFormRef)">保存</el-button>
        <el-button @click="$emit('update:dialogVisible', false)">取消</el-button>
      </div>
    </el-dialog>
  </div>
 </template>
 <script lang="ts" setup>
 import { reactive, watch, ref } from 'vue'
 import { type FormInstance, type FormRules, ElMessage } from 'element-plus'
 import SystemApi from '@/api/SystemManagement'
 // 定义要发送的emit事件
let $emit = defineEmits(['update:dialogVisible', 'callback'])
 // 参数管理
const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  dialogForm: {
    type: Object,
    default: () => {
      return {}
    }
  }
})
const state = reactive({
  visible: false,
  form: {
    id: '',
    value: '',
    keyId: '',
    label: '',
    description: ''
  }
})

// form 表单校验
const ruleFormRef = ref<FormInstance>()
const rules = reactive<FormRules>({
  label: [{ required: true, message: '请输入字典值', trigger: 'blur' }],
  value: [{ required: true, message: '请输入字典值', trigger: 'blur' }]
})

// 弹窗-保存
const submitForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.validate((valid) => {
    if (valid) {
      let apiName = 'saveSysDictValue'
      if (state.form.id) {
        apiName = 'updateSysDictValue'
      }
      const params = {
        id: state.form.id,
        keyId: state.form.keyId,
        value: state.form.value,
        label: state.form.label,
        description: state.form.description
      } as any
      (SystemApi as any)[apiName](params).then((res: any) => {
        if (res.code === '0000') {
          ElMessage({type: 'success', message: '操作成功'})
          $emit('callback')
          $emit('update:dialogVisible', false)
        } else {
          ElMessage({ message: res.message, type: 'error' })
        }
      })
    }
  })
}

// 重置弹窗
const resetDialog = () => {
  state.form = {
    id: '',
    value: '',
    keyId: '',
    label: '',
    description: ''
  }
  $emit('update:dialogVisible', false)
}

watch(
  () => props.dialogVisible,
  (newVal) => {
    state.visible = newVal
    if (newVal) {
      state.form = {...state.form, ...props.dialogForm}
      console.log(state.form)
    }
  }
)
</script>
<style lang="scss" scoped>
.el-select__wrapper {
  width: 200px;
}
.dialog-input .el-input {
  width: 260px;
}
.dialog-input ::deep(.el-textarea__inner) {
  width: 260px;
}
.dialog-input .el-select__wrapper {
  width: 260px;
}
.el-message-box__container {
  align-items: normal;
}
.dialog-title {
  margin-bottom: 10px;
  text-align: center;
  font-size: 17px;
  font-weight: bold;
}
</style>