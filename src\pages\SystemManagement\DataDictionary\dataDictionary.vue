<!--产品管理-->
<template>
  <div>
    <el-form :inline="true" :model="search" ref="searchForm">
      <el-form-item label="字典编码" prop="keyName">
        <el-input v-model="search.keyName" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="字典名称" prop="keyLabel">
        <el-input v-model="search.keyLabel" placeholder="请输入" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getSysDictKeyPage(1)">查询<el-icon class="el-icon--right"><Search /></el-icon></el-button>
        <el-button @click="resetFormData(searchForm)">重置<el-icon class="el-icon--right"><RefreshLeft /></el-icon></el-button>
      </el-form-item>
    </el-form>
    <el-button class="mb10" type="primary" @click="showAddEditDictDialog({})">添加</el-button>
    <el-table :data="state.tableData" border stripe v-loading="loading" :cell-style="{ textAlign: 'center' }" :header-cell-style="{ textAlign: 'center' }">
      <el-table-column type="index" width="60" label="序号"/>
      <el-table-column prop="keyName" label="字典编码"/>
      <el-table-column prop="keyLabel" label="字典名称"/>
      <el-table-column prop="description" label="描述"/>
      <el-table-column prop="createTime" label="创建时间"/>
      <el-table-column prop="updateTime" label="更新时间"/>
      <el-table-column label="操作" width="180">
        <template #default="{ row }">
          <el-button type="primary" text @click="showDictValueDialog(row.id)">字典值</el-button>
          <el-button type="primary" text @click="showAddEditDictDialog(row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="mt20 flex flex_center">
      <el-pagination
        v-model:current-page="pagination.pageNum"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[5, 10, 20, 50, 100]"
        :background="true"
        layout="prev, pager, next, jumper, sizes, total"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <AddEditDictDialog v-model:dialogVisible="addEditDictDialog.show" :dialogForm="addEditDictDialog.row" @callback="addEditDictDialog.callback"></AddEditDictDialog>
    <DictValueListDialog v-model:dialogVisible="dictValueListDialog.show" :keyId="dictValueListDialog.keyId"></DictValueListDialog>
  </div>
</template>

<script setup lang="ts">
import {onMounted, reactive, ref} from 'vue'
import {ElMessage} from 'element-plus'
import type { FormInstance } from 'element-plus'
import { Search, RefreshLeft } from '@element-plus/icons-vue'
import AddEditDictDialog from './components/AddEditDictDialog.vue'
import DictValueListDialog from './components/DictValueListDialog.vue'
import SystemApi from '@/api/SystemManagement'
const searchForm = ref<FormInstance>()

// 搜索数据
const search = reactive({
  keyName: '', // 字典编码
  keyLabel: ''
})

// 重置表单
const resetFormData = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}

// 表格
let state = reactive({
  tableData: [] as any
})
const loading = ref(false)
let pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})
// 获取字典列表
const getSysDictKeyPage = (pageNum:number) => {
  const params = {
    pageNum: pageNum || 1,
    pageSize: pagination.pageSize,
    keyName: search.keyName,
    keyLabel: search.keyLabel
  }
  loading.value = true
  SystemApi.getSysDictKeyPage(params).then((res: any) => {
    loading.value = false
    if (res.code === '0000') {
      state.tableData = res.data.records
      pagination.total = res.data.total
      pagination.pageNum = res.data.current
    } else {
      ElMessage({ message: res.message, type: 'error' })
    }
  })
}

const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  getSysDictKeyPage(1)
}
const handleCurrentChange = (val: number) => {
  pagination.pageNum = val
  getSysDictKeyPage(val)
}

// 新增编辑字典弹窗
const addEditDictDialog = reactive({
  show: false,
  row: {},
  callback: (data: any) => {}
})

// 打开新增编辑字典弹窗
const showAddEditDictDialog = (row: any) => {
  addEditDictDialog.row = row
  addEditDictDialog.show = true
  addEditDictDialog.callback = (data: any) => {
    getSysDictKeyPage(1)
    console.log(data)
  }
}

// 字典值弹窗
const dictValueListDialog = reactive({
  show: false,
  keyId: 0 // 字典id
})

// 字典值弹窗
const showDictValueDialog = (keyId: number) => {
  dictValueListDialog.show = true
  dictValueListDialog.keyId = keyId
}

onMounted(() => {
  getSysDictKeyPage(1)
})
</script>

<style lang="scss" scoped>
.el-select__wrapper {
  width: 200px;
}
.dialog-input .el-input {
  width: 260px;
}
.dialog-input .el-textarea__inner {
  width: 260px;
}
.dialog-input .el-select__wrapper {
  width: 260px;
}
.el-message-box__container {
  align-items: normal;
}
.dialog-title {
  margin-bottom: 10px;
  text-align: center;
  font-size: 17px;
  font-weight: bold;
}
.configure-input {
  margin-right: 10px;
  width: 230px;
}
</style>

